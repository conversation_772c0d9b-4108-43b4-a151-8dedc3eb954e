package v1

type SaveAboutRequest struct {
	Img      string `json:"img" `
	ChTitle  string `json:"ch_title" `
	EnTitle  string `json:"en_title" `
	Email    string `json:"email"  `
	Address  string `json:"address" `
	FollowUs string `json:"follow_us"  `
}
type UpdateAboutRequest struct {
	Id       int    `form:"id" `
	Img      string `json:"img" `
	ChTitle  string `json:"ch_title"  `
	EnTitle  string `json:"en_title"  `
	Email    string `json:"email" `
	Address  string `json:"address"  `
	FollowUs string `json:"follow_us"`
}

type GetAboutRequest struct {
	//ID int64 `uri:"id" binding:"required,min=1"` // ID从URL路径中获取
}

type GetAboutResponse struct {
	ID       uint   `json:"id"`
	Img      string `json:"img"`
	ChTitle  string `json:"ch_title"`
	EnTitle  string `json:"en_title"`
	Email    string `json:"email"`
	Address  string `json:"address"`
	FollowUs string `json:"follow_us"`
}
