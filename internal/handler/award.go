package handler

import (
	"github.com/gin-gonic/gin"
	"nunu-layout-admin/internal/service"
)

type AwardHandler struct {
	*Handler
	awardService service.AwardService
}

func NewAwardHandler(
	handler *Handler,
	awardService service.AwardService,
) *AwardHandler {
	return &AwardHandler{
		Handler:      handler,
		awardService: awardService,
	}
}

func (h *AwardHandler) GetAward(ctx *gin.Context) {

}
