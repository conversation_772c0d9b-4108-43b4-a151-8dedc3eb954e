package handler

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"
)

type MessageIndexHandler struct {
	*Handler
	messageIndexService service.MessageIndexService
}

func NewMessageIndexHandler(
	handler *Handler,
	messageIndexService service.MessageIndexService,
) *MessageIndexHandler {
	return &MessageIndexHandler{
		Handler:             handler,
		messageIndexService: messageIndexService,
	}
}

func (h *MessageIndexHandler) CreateMessageIndex(ctx *gin.Context) {
	var req v1.SaveMessageIndexRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.messageIndexService.MessageIndexCreate(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

func (h *MessageIndexHandler) UpdateMessageIndex(ctx *gin.Context) {
	var req v1.UpdateMessageIndexRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.messageIndexService.MessageIndexUpdate(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}
func (h *MessageIndexHandler) DeleteMessageIndex(ctx *gin.Context) {
	var req v1.DeleteMessageIndexRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.messageIndexService.MessageIndexDelete(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// MessageIndexHandler 增加列表方法
func (h *MessageIndexHandler) GetMessageIndexList(ctx *gin.Context) {
	var req v1.MessageIndexListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层获取列表
	list, err := h.messageIndexService.MessageIndexList(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	//// 构建响应
	//resp := &v1.MessageIndexListResponse{
	//	Total: total,
	//	List:  make([]*v1.MessageIndexItem, len(list)),
	//}
	//
	//// 转换模型为API响应格式
	//for i, item := range list {
	//	resp.List[i] = &v1.MessageIndexItem{
	//		ID:      item.ID,
	//		Project: item.Project,
	//		Type:    item.Type,
	//		Client:  item.Client,
	//		Year:    item.Year,
	//	}
	//}

	v1.HandleSuccess(ctx, list)
}

func (h *MessageIndexHandler) GetMessageIndexDetail(ctx *gin.Context) {
	var req v1.MessageIndexDetailRequest
	if err := ctx.ShouldBindUri(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层获取详情
	item, err := h.messageIndexService.MessageIndexDetail(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			v1.HandleError(ctx, http.StatusNotFound, v1.ErrNotFound, nil)
		} else {
			v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		}
		return
	}

	// 构建响应
	resp := &v1.MessageIndexDetailResponse{
		ID:      item.ID,
		Project: item.Project,
		Type:    item.Type,
		Client:  item.Client,
		Year:    item.Year,
	}

	v1.HandleSuccess(ctx, resp)
}
