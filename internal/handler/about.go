package handler

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"
)

type AboutHandler struct {
	*Handler
	aboutService service.AboutService
}

func NewAboutHandler(
	handler *Handler,
	aboutService service.AboutService,
) *AboutHandler {
	return &AboutHandler{
		Handler:      handler,
		aboutService: aboutService,
	}
}

func (h *AboutHandler) GetAbout(ctx *gin.Context) {
	//var req v1.GetAboutRequest
	//
	//if err := ctx.ShouldBindUri(&req); err != nil {
	//	v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
	//	return
	//}

	// 调用服务层获取详情
	item, err := h.aboutService.GetAbout(ctx)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			v1.HandleError(ctx, http.StatusNotFound, v1.ErrNotFound, nil)
		} else {
			v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		}
		return

	}

	resp := &v1.GetAboutResponse{
		ID:       item.ID,
		Img:      item.Img,
		ChTitle:  item.ChTitle,
		EnTitle:  item.EnTitle,
		FollowUs: item.FollowUs,
		Email:    item.Email,
		Address:  item.Address,
	}
	v1.HandleSuccess(ctx, resp)
}

func (h *AboutHandler) CreateAbout(ctx *gin.Context) {
	var req v1.SaveAboutRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.aboutService.CreateAbout(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)

}

func (h *AboutHandler) UpdateAbout(ctx *gin.Context) {
	var req v1.UpdateAboutRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.aboutService.UpdateAbout(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}
