package server

import (
	"embed"
	"html/template"
	nethttp "net/http"
	"nunu-layout-admin/docs"
	"nunu-layout-admin/internal/handler"
	"nunu-layout-admin/internal/middleware"
	"nunu-layout-admin/pkg/jwt"
	"nunu-layout-admin/pkg/log"
	"nunu-layout-admin/pkg/server/http"
	"nunu-layout-admin/web"
	"strings"

	"github.com/casbin/casbin/v2"
	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

//go:embed template/*
var templates embed.FS

// 将文本中的换行符转换为HTML的<br>标签
func nl2br(text string) template.HTML {
	return template.HTML(strings.Replace(template.HTMLEscapeString(text), "\n", "<br>", -1))
}

func NewHTTPServer(
	logger *log.Logger,
	conf *viper.Viper,
	jwt *jwt.JWT,
	e *casbin.SyncedEnforcer,
	adminHandler *handler.AdminHandler,
	messageIndexHandler *handler.MessageIndexHandler,
	messageWorkHandler *handler.MessageWorkHandler,
	userHandler *handler.UserHandler,
	upyunHandler *handler.UpyunHandler,
	categoryHandler *handler.CategoryHandler,
	memberHandler *handler.MemberHandler,
	aboutHandler *handler.AboutHandler,
	frontendHandler *handler.FrontendHandler,
	detailHandler *handler.MessageIndexDetailHandler,
	// upyunHandler
) *http.Server {
	gin.SetMode(gin.DebugMode)
	s := http.NewServer(
		gin.Default(),
		logger,
		http.WithServerHost(conf.GetString("http.host")),
		http.WithServerPort(conf.GetInt("http.port")),
	)
	// 定义自定义模板函数

	// 使用template.Must简化错误处理并链式调用函数
	tmpl := template.Must(template.New("").Funcs(template.FuncMap{
		"hasSuffix": strings.HasSuffix,
		"nl2br":     nl2br,
	}).ParseFS(templates, "template/*.html"))

	s.SetHTMLTemplate(tmpl)

	s.GET("", frontendHandler.Index)
	s.GET("index", frontendHandler.Index)
	s.GET("list", frontendHandler.List)
	s.GET("works", frontendHandler.Works)
	s.GET("about", frontendHandler.About)
	s.GET("work_detail/:id", frontendHandler.WorkDetail)
	// 设置字体文件路由

	// 设置前端静态资源
	fs, err := static.EmbedFolder(web.Assets(), "dist")
	if err != nil {
		// 处理错误，例如记录日志或者终止程序
		panic(err)
	}

	s.Use(static.Serve("", fs))
	s.NoRoute(func(c *gin.Context) {
		indexPageData, err := web.Assets().ReadFile("dist/index.html")
		if err != nil {
			c.String(nethttp.StatusNotFound, "404 page not found")
			return
		}
		c.Data(nethttp.StatusOK, "text/html; charset=utf-8", indexPageData)
	})
	// swagger doc
	docs.SwaggerInfo.BasePath = "/"
	s.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerfiles.Handler,
		//ginSwagger.URL(fmt.Sprintf("http://localhost:%d/swagger/doc.json", conf.GetInt("app.http.port"))),
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
	))

	s.Use(
		middleware.CORSMiddleware(),
		// middleware.ResponseLogMiddleware(logger),
		// middleware.RequestLogMiddleware(logger),
		//middleware.SignMiddleware(log),
	)

	v1 := s.Group("/v1")
	{
		// No route group has permission
		noAuthRouter := v1.Group("/")
		{
			noAuthRouter.POST("/login", adminHandler.Login)
		}

		// Strict permission routing group
		strictAuthRouter := v1.Group("/").Use(middleware.StrictAuth(jwt, logger), middleware.AuthMiddleware(e))
		{
			strictAuthRouter.GET("/users", userHandler.GetUsers)

			strictAuthRouter.GET("/menus", adminHandler.GetMenus)
			strictAuthRouter.GET("/admin/menus", adminHandler.GetAdminMenus)
			strictAuthRouter.POST("/admin/menu", adminHandler.MenuCreate)
			strictAuthRouter.PUT("/admin/menu", adminHandler.MenuUpdate)
			strictAuthRouter.DELETE("/admin/menu", adminHandler.MenuDelete)

			strictAuthRouter.GET("/admin/users", adminHandler.GetAdminUsers)
			strictAuthRouter.GET("/admin/user", adminHandler.GetAdminUser)
			strictAuthRouter.PUT("/admin/user", adminHandler.AdminUserUpdate)
			strictAuthRouter.POST("/admin/user", adminHandler.AdminUserCreate)
			strictAuthRouter.DELETE("/admin/user", adminHandler.AdminUserDelete)
			strictAuthRouter.GET("/admin/user/permissions", adminHandler.GetUserPermissions)
			strictAuthRouter.GET("/admin/role/permissions", adminHandler.GetRolePermissions)
			strictAuthRouter.PUT("/admin/role/permission", adminHandler.UpdateRolePermission)
			strictAuthRouter.GET("/admin/roles", adminHandler.GetRoles)
			strictAuthRouter.POST("/admin/role", adminHandler.RoleCreate)
			strictAuthRouter.PUT("/admin/role", adminHandler.RoleUpdate)
			strictAuthRouter.DELETE("/admin/role", adminHandler.RoleDelete)

			strictAuthRouter.GET("/admin/apis", adminHandler.GetApis)
			strictAuthRouter.POST("/admin/api", adminHandler.ApiCreate)
			strictAuthRouter.PUT("/admin/api", adminHandler.ApiUpdate)
			strictAuthRouter.DELETE("/admin/api", adminHandler.ApiDelete)

			//messageIndex
			strictAuthRouter.POST("/messageIndex/create", messageIndexHandler.CreateMessageIndex)
			strictAuthRouter.PUT("/messageIndex/update", messageIndexHandler.UpdateMessageIndex)
			strictAuthRouter.DELETE("/messageIndex/delete", messageIndexHandler.DeleteMessageIndex)
			strictAuthRouter.GET("/messageIndex/list", messageIndexHandler.GetMessageIndexList)
			strictAuthRouter.GET("/messageIndex/detail/:id", messageIndexHandler.GetMessageIndexDetail)

			//messageWork
			strictAuthRouter.POST("/messageWork/create", messageWorkHandler.Create)
			strictAuthRouter.PUT("/messageWork/update", messageWorkHandler.Update)
			strictAuthRouter.DELETE("/messageWork/delete", messageWorkHandler.Delete)
			strictAuthRouter.GET("/messageWork/list", messageWorkHandler.List)
			strictAuthRouter.GET("/messageWork/detail/:id", messageWorkHandler.Detail)

			strictAuthRouter.GET("/upyun/signature", upyunHandler.GenerateSignature)
			//分类
			strictAuthRouter.POST("/category/create", categoryHandler.CreateCategory)
			strictAuthRouter.PUT("/category/update", categoryHandler.UpdateCategory)
			strictAuthRouter.GET("/category/detail/:id", categoryHandler.GetCategory)
			strictAuthRouter.GET("/category/list", categoryHandler.ListCategory)
			strictAuthRouter.DELETE("/category/delete", categoryHandler.DeleteCategory)

			//成员
			strictAuthRouter.POST("/member/create", memberHandler.CreateMember)
			strictAuthRouter.PUT("/member/update", memberHandler.UpdateMember)
			strictAuthRouter.DELETE("/member/delete", memberHandler.DeleteMember)
			strictAuthRouter.GET("/member/list", memberHandler.ListMember)

			//关于我们
			strictAuthRouter.POST("/about/create", aboutHandler.CreateAbout)
			strictAuthRouter.PUT("/about/update", aboutHandler.UpdateAbout)
			strictAuthRouter.GET("/about/detail", aboutHandler.GetAbout)

			//图片详情
			strictAuthRouter.POST("/messageIndexDetail/createOrUpdate", detailHandler.CreateOrUpdateMessageIndexDetail)
			strictAuthRouter.GET("/messageIndexDetail/GetMessageIndexDetail/:IndexId", detailHandler.GetMessageIndexDetail)

		}
	}
	return s
}
