<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>A ONE AND A TWO DESIGN</title>
    <script src="//static.aoneandatwodesign.com/static/style/tailwind.js"></script>
    <link
      rel="stylesheet"
      href="//static.aoneandatwodesign.com/static/style/all.min.css"
    />

    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .global-selection {
          @apply selection:bg-black selection:text-white;
        }
        .mobile-padding {
          @apply px-[clamp(1rem,3vw,3.5rem)];
        }
        .mobile-column {
          @apply flex-col md:flex-row;
        }

        .column-spacing {
          @apply lg:pr-[clamp(2rem,4vw,5rem)];
        }
        .mobile-divider {
          @apply md:hidden border-t border-gray-200 my-[clamp(2rem,5vw,3rem)];
        }
        .project-card {
          @apply mb-6 p-4 border border-gray-800 rounded-lg ;
        }
        .project-title {
          @apply text-[clamp(1.2rem,3vw,1.5rem)] font-medium mb-2;
        }
        .project-details {
          @apply text-[clamp(0.9rem,2vw,1rem)] text-gray-400;
        }
        .project-year {
          @apply text-right text-gray-500;
        }
      }
      body {
        @apply global-selection;
      }

      .text-cn {
        transform: scaleY(0.95);
        transform-origin: center;
        display: inline-block;
      }

      /* 导入自定义字体 */
      @font-face {
      font-family: "Bradford";
      src: url("//static.aoneandatwodesign.com/static/fonts/BradfordLL-Book.otf") format("opentype");
      }

      @font-face {
      font-family: "SourceHanSerifCN-Regular";
      src: url("//static.aoneandatwodesign.com/static/fonts/SourceHanSerifCN-Regular.otf") format("opentype");
      }
      </style>
      <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              // 设置字体优先级
            mixed: ['Bradford', 'SourceHanSerifCN-Regular', 'serif'],
            },
          // 添加选中文本的样式
          textSelection: {
            'selected': {
              backgroundColor: '#000',
              color: '#fff'
            }
          }
          },
        },
      };
      </script>

  </head>
  <body class="base-1920 bg-black text-white font-mixed font-light antialiased">
   
  <!-- 导航栏 -->
  <nav class="w-full transition-all duration-300" id="main-nav">
    <div class="flex justify-between items-center text-[32px] leading-[28px]  mt-[45px] mx-[56px]">
      <div class="flex-shrink-0">
        <a href="index.html">
          <span style="letter-spacing: -0.05em">a</span
          ><span style="letter-spacing: -0.03em">O</span
          ><span style="letter-spacing: -0.04em">n</span
          ><span style="letter-spacing: -0.03em">e</span
          ><span style="letter-spacing: -0.03em">a</span
          ><span style="letter-spacing: -0.04em">n</span
          ><span style="letter-spacing: -0.03em">d</span
          ><span style="letter-spacing: -0.122em">a</span
          ><span style="letter-spacing: -0.14em">T</span
          ><span style="letter-spacing: -0.04em">w</span>o
        </a>
      </div>
      <!-- 桌面导航 -->
      <div class="hidden md:flex" >
        <a href="/works" class="mr-[clamp(2.4375rem,3vw,2.4375rem)]">work</a>
        <a href="/list" class="mr-[clamp(2.125rem,3vw,2.125rem)]">index</a>
        <a href="/about" class="">about</a>
      </div>
      <!-- 移动端菜单按钮 -->
      <button id="menu-toggle" class="md:hidden text-2xl">
        <i class="fa fa-bars"></i>
      </button>
    </div>
    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="hidden md:hidden bg-black absolute w-full z-10 shadow-lg mobile-padding">
      <div class="flex flex-col py-4">
        <a href="/works" class="py-2">work</a>
        <a href="/about" class="py-2">about</a>
      </div>
    </div>
  </nav>
  
  <main id="list" class="mx-[8.23vw] mt-[3.65vw] text-[23px] leading-[28px]">
    <!-- 桌面端表格布局 -->
    <div class="md:block hidden">
      <table class="border-0 p-0">
        <thead>
          <tr>
            <th class="border-0 pb-[clamp(1.667vw,32px,32px)] text-left w-[clamp(56px,5.781vw,5.781vw)] p-0 font-normal"></th>
            <th class="border-0 pb-[clamp(1.667vw,32px,32px)] text-left w-[clamp(677px,35.26vw,35.26vw)] p-0 font-normal">project</th> 
            <th class="border-0 pb-[clamp(1.667vw,32px,32px)] text-left w-[clamp(350px,18.229vw,18.229vw)] p-0 font-normal">type</th>
            <th class="border-0 pb-[clamp(1.667vw,32px,32px)] text-left w-[clamp(392px,20.416vw,20.416vw)] p-0 font-normal">client</th>
            <th class="border-0 pb-[clamp(1.667vw,32px,32px)] text-left p-0 font-normal">year</th>
          </tr>
        </thead>
        <tbody class="cursor-pointer">
          {{range .Items}}
          <tr data-url="{{.URL}}" class="leading-[clamp(1.458vw,28px,28px)] hover:opacity-50 transition-opacity align-top">
            <td class="border-0 p-0">{{.ID}}.</td>
            <td class="border-0 p-0">{{ nl2br .Title}}</td>
            <td class="border-0 p-0">{{ nl2br .Type}}</td>
            <td class="border-0 p-0">{{ nl2br .Client}}</td>
            <td class="border-0 p-0">{{ nl2br .Year}}</td>
          </tr>
          {{end}}
        </tbody>
      </table>
    </div>
  
    <!-- 移动端卡片布局 -->
    <div class="md:hidden">
      {{range .Items}}
      <div class="project-card" data-category="{{.Type}}">
        <div class="project-title">{{.ID}}. {{.Title}}</div>
        <div class="project-details">
          <div class="mb-1">type: {{ nl2br .Type}}</div>
          <div class="mb-1">client: {{ nl2br .Client}}</div>
          <div class="mb-1">year: {{ nl2br .Year}}</div>
        </div>
      </div>
      {{end}}
    </div>
  </main>
  
      <!-- 移动端卡片布局 -->
      <div class="md:hidden">
        <div class="project-card">
          <div class="project-title">1. Polyphony & materiality of book design</div>
          <div class="project-details">
            <div class="mb-1">type: poster</div>
            <div class="mb-1">client: reserve land libraly</div>
            <div class="mb-1">year: 2025</div>
          </div>
        </div>
        
        <div class="project-card">
          <div class="project-title">2. Experimental Typography Exhibition at Shanghai Design Week</div>
          <div class="project-details">
            <div class="mb-1">type: exhibitions</div>
            <div class="mb-1">client: taikoohui</div>
            <div class="mb-1">year: 2024</div>
          </div>
        </div>
        
        <div class="project-card">
          <div class="project-title">3. A room leaving room for __</div>
          <div class="project-details">
            <div class="mb-1">type: books/logo</div>
            <div class="mb-1">client: gallery</div>
            <div class="mb-1">year: 2024</div>
          </div>
        </div>
                    <!-- 项目4 -->
                    <div class="project-card" data-category="exhibitions poster">
                      <div class="project-title">4. Irma boom</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 展览/海报</div>
                          <div class="mb-1">客户: design 360</div>
                          <div class="mb-1">年份: 2024</div>
                      </div>
                  </div>
      
                  <!-- 项目5 -->
                  <div class="project-card" data-category="books">
                      <div class="project-title">5. Tre asiatiska översättningar av b.</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 书籍</div>
                          <div class="mb-1">客户: reserve land libraly</div>
                          <div class="mb-1">年份: 2024</div>
                      </div>
                  </div>
      
                  <!-- 项目6 -->
                  <div class="project-card" data-category="books">
                      <div class="project-title">6. Design and more</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 书籍</div>
                          <div class="mb-1">客户: taikoohui</div>
                          <div class="mb-1">年份: 2024</div>
                      </div>
                  </div>
      
                  <!-- 项目7 -->
                  <div class="project-card" data-category="magazine">
                      <div class="project-title">7. Spaper autumn issue</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志</div>
                          <div class="mb-1">客户: gallery</div>
                          <div class="mb-1">年份: 2023</div>
                      </div>
                  </div>
      
                  <!-- 项目8 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">8. Shaping visual culture 13 designers in japan</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: design 360</div>
                          <div class="mb-1">年份: 2023</div>
                      </div>
                  </div>
      
                  <!-- 项目9 -->
                  <div class="project-card" data-category="books">
                      <div class="project-title">9. The sound of journey</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 书籍</div>
                          <div class="mb-1">客户: reserve land libraly</div>
                          <div class="mb-1">年份: 2023</div>
                      </div>
                  </div>
      
                  <!-- 项目10 -->
                  <div class="project-card" data-category="magazine logo">
                      <div class="project-title">10. Reserve land</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志/标志</div>
                          <div class="mb-1">客户: taikoohui</div>
                          <div class="mb-1">年份: 2023</div>
                      </div>
                  </div>
      
                  <!-- 项目11 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">11. Taiping shuilihui</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: gallery</div>
                          <div class="mb-1">年份: 2023</div>
                      </div>
                  </div>
      
                  <!-- 项目12 -->
                  <div class="project-card" data-category="books">
                      <div class="project-title">12. Books and the city</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 书籍</div>
                          <div class="mb-1">客户: design 360</div>
                          <div class="mb-1">年份: 2023</div>
                      </div>
                  </div>
      
                  <!-- 项目13 -->
                  <div class="project-card" data-category="magazine">
                      <div class="project-title">13. Lives on the soil</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志</div>
                          <div class="mb-1">客户: reserve land libraly</div>
                          <div class="mb-1">年份: 2022</div>
                      </div>
                  </div>
      
                  <!-- 项目14 -->
                  <div class="project-card" data-category="brand logo">
                      <div class="project-title">14. Magazine world's dreading journey</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌/标志</div>
                          <div class="mb-1">客户: taikoohui</div>
                          <div class="mb-1">年份: 2022</div>
                      </div>
                  </div>
      
                  <!-- 项目15 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">15. Sugar free x all cross cafe</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: gallery</div>
                          <div class="mb-1">年份: 2022</div>
                      </div>
                  </div>
      
                  <!-- 项目16 -->
                  <div class="project-card" data-category="exhibitions">
                      <div class="project-title">16. Archaeology in the time and space</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 展览</div>
                          <div class="mb-1">客户: design 360</div>
                          <div class="mb-1">年份: 2022</div>
                      </div>
                  </div>
      
                  <!-- 项目17 -->
                  <div class="project-card" data-category="exhibitions">
                      <div class="project-title">17. Spaper winter issue</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 展览</div>
                          <div class="mb-1">客户: reserve land libraly</div>
                          <div class="mb-1">年份: 2022</div>
                      </div>
                  </div>
      
                  <!-- 项目18 -->
                  <div class="project-card" data-category="exhibitions">
                      <div class="project-title">18. Polyphony & materiality of book design</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 展览</div>
                          <div class="mb-1">客户: taikoohui</div>
                          <div class="mb-1">年份: 2021</div>
                      </div>
                  </div>
      
                  <!-- 项目19 -->
                  <div class="project-card" data-category="magazine">
                      <div class="project-title">19. Chillland</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志</div>
                          <div class="mb-1">客户: gallery</div>
                          <div class="mb-1">年份: 2021</div>
                      </div>
                  </div>
      
                  <!-- 项目20 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">20. A room leaving room for __</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: design 360</div>
                          <div class="mb-1">年份: 2021</div>
                      </div>
                  </div>
      
                  <!-- 项目21 -->
                  <div class="project-card" data-category="books">
                      <div class="project-title">21. Irma boom</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 书籍</div>
                          <div class="mb-1">客户: reserve land libraly</div>
                          <div class="mb-1">年份: 2021</div>
                      </div>
                  </div>
      
                  <!-- 项目22 -->
                  <div class="project-card" data-category="magazine">
                      <div class="project-title">22. Tre asiatiska översättningar av b.</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志</div>
                          <div class="mb-1">客户: taikoohui</div>
                          <div class="mb-1">年份: 2021</div>
                      </div>
                  </div>
      
                  <!-- 项目23 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">23. Design and more</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: gallery</div>
                          <div class="mb-1">年份: 2021</div>
                      </div>
                  </div>
      
                  <!-- 项目24 -->
                  <div class="project-card" data-category="books">
                      <div class="project-title">24. Spaper autumn issue</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 书籍</div>
                          <div class="mb-1">客户: design 360</div>
                          <div class="mb-1">年份: 2021</div>
                      </div>
                  </div>
      
                  <!-- 项目25 -->
                  <div class="project-card" data-category="magazine">
                      <div class="project-title">25. Shaping visual culture 13 designers in japan</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志</div>
                          <div class="mb-1">客户: reserve land libraly</div>
                          <div class="mb-1">年份: 2020</div>
                      </div>
                  </div>
      
                  <!-- 项目26 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">26. The sound of journey</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: taikoohui</div>
                          <div class="mb-1">年份: 2020</div>
                      </div>
                  </div>
      
                  <!-- 项目27 -->
                  <div class="project-card" data-category="magazine">
                      <div class="project-title">27. Reserve land</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志</div>
                          <div class="mb-1">客户: gallery</div>
                          <div class="mb-1">年份: 2020</div>
                      </div>
                  </div>
      
                  <!-- 项目28 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">28. Taiping shuilihui</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: design 360</div>
                          <div class="mb-1">年份: 2020</div>
                      </div>
                  </div>
      
                  <!-- 项目29 -->
                  <div class="project-card" data-category="books">
                      <div class="project-title">29. Books and the city</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 书籍</div>
                          <div class="mb-1">客户: reserve land libraly</div>
                          <div class="mb-1">年份: 2020</div>
                      </div>
                  </div>
      
                  <!-- 项目30 -->
                  <div class="project-card" data-category="magazine">
                      <div class="project-title">30. Lives on the soil</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志</div>
                          <div class="mb-1">客户: taikoohui</div>
                          <div class="mb-1">年份: 2019</div>
                      </div>
                  </div>
      
                  <!-- 项目31 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">31. Magazine world's dreading journey</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: gallery</div>
                          <div class="mb-1">年份: 2019</div>
                      </div>
                  </div>
      
                  <!-- 项目32 -->
                  <div class="project-card" data-category="books">
                      <div class="project-title">32. Sugar free x all cross cafe</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 书籍</div>
                          <div class="mb-1">客户: design 360</div>
                          <div class="mb-1">年份: 2019</div>
                      </div>
                  </div>
      
                  <!-- 项目33 -->
                  <div class="project-card" data-category="magazine">
                      <div class="project-title">33. Archaeology in the time and space</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 杂志</div>
                          <div class="mb-1">客户: reserve land libraly</div>
                          <div class="mb-1">年份: 2019</div>
                      </div>
                  </div>
      
                  <!-- 项目34 -->
                  <div class="project-card" data-category="brand">
                      <div class="project-title">34. Spaper winter issue</div>
                      <div class="project-details">
                          <div class="mb-1">类型: 品牌</div>
                          <div class="mb-1">客户: taikoohui</div>
                          <div class="mb-1">年份: 2019</div>
                      </div>
                  </div>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="pt-[clamp(8.75vw,168px,168px)] pb-[clamp(12.604vw,242px,242px)]">
      <div class="mx-auto">
        <p class="text-center text-[14px] leading-[20px] font-regular">
          © 2019-2025 a one and a two design. All rights reserved
        </p>
      </div>
    </footer>
      
    <!-- 回到顶部按钮 -->
    <button onclick="scrollToTop()" id="backToTop" class="fixed bottom-[clamp(1.5rem,2vw,56px)] right-[clamp(1.5rem,2vw,56px)] w-[clamp(3rem,5vw,3.5rem)] h-[clamp(3rem,5vw,3.5rem)] flex items-center justify-center   mix-blend-exclusion">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="240 380 50 80" class="w-[clamp(3rem,5vw,3.5rem)] h-[clamp(3rem,5vw,3.5rem)]">
        <path d="M263.79,389.4l14.51,15.38h-8.76v24.72h-11.51v-24.72h-8.76L263.79,389.4z M263.79,386.93l-18.48,19.55h11.03v24.72h14.9v-24.72h11.03L263.79,386.93z" fill="white" />
      </svg>
    </button>

    <script>
      // 移动端菜单切换
      const menuToggle = document.getElementById('menu-toggle');
      const mobileMenu = document.getElementById('mobile-menu');
      
      menuToggle.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
      
      // 控制回到顶部按钮的显示和隐藏
      window.onscroll = function() {
        const backToTopButton = document.getElementById('backToTop');
        if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
          backToTopButton.classList.remove('opacity-0', 'invisible');
          backToTopButton.classList.add('opacity-100', 'visible');
        } else {
          backToTopButton.classList.remove('opacity-100', 'visible');
          backToTopButton.classList.add('opacity-0', 'invisible');
        }
      };

      // 平滑滚动到顶部
      function scrollToTop() {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }

      
      // 获取所有<tr>元素
      const rows = document.querySelectorAll('tr');
      
      rows.forEach(row => {
        row.addEventListener('click', () => {
          // 获取自定义属性 data-url 中的链接
          const url = row.dataset.url;
          if (url) {
            window.location.href = url; // 跳转链接
          }
        });
      });
    </script>
  </body>
</html>
    