package service

import (
	"context"
	"nunu-layout-admin/internal/model"
	"nunu-layout-admin/internal/repository"
)

type AwardService interface {
	GetAward(ctx context.Context, id int64) (*model.Award, error)
}

func NewAwardService(
	service *Service,
	awardRepository repository.AwardRepository,

) AwardService {
	return &awardService{
		Service:         service,
		awardRepository: awardRepository,
	}
}

type awardService struct {
	*Service
	awardRepository repository.AwardRepository
}

func (s *awardService) GetAward(ctx context.Context, id int64) (*model.Award, error) {
	return s.awardRepository.GetAward(ctx, id)
}
