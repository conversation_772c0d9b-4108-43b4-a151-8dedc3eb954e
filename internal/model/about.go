package model

import "gorm.io/gorm"

type About struct {
	gorm.Model
	//ID      string `json:"id" gorm:"type:varchar(255);not null;comment:'图片路径'"`
	Img      string `json:"img" gorm:"type:varchar(255);not null;comment:'图片路径'"`
	ChTitle  string `json:"ch_title" gorm:"type:varchar(100);not null;comment:'中文标签'"`
	EnTitle  string `json:"en_title" gorm:"type:varchar(100);not null;comment:'英文标签'"`
	Address  string `json:"address" gorm:"type:varchar(100);not null;comment:'地址'"`
	Email    string `json:"email" gorm:"type:varchar(100);not null;comment:'邮箱'"`
	FollowUs string `json:"follow_us" gorm:"type:varchar(100);not null;comment:'关注我们'"`
}

func (m *About) TableName() string {
	return "about"
}
