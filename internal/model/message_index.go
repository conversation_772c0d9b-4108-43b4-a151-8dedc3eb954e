package model

import "gorm.io/gorm"

type MessageIndex struct {
	gorm.Model
	Client       string `json:"client" gorm:"type:varchar(100);not null;comment:'链接'"`
	Type         string `json:"type" gorm:"type:varchar(255);not null;comment:'类型'"`
	Project      string `json:"project" gorm:"type:varchar(100);not null;comment:'项目'"`
	Year         int    `json:"year" gorm:"column:year;type:int;default:0;comment:年份"`
	ChTitle      string `json:"ch_title" gorm:"type:varchar(255);not null;comment:'中文标签'"`
	EnTitle      string `json:"en_title" gorm:"type:varchar(255);not null;comment:'英文标签'"`
	Content      string `json:"content" gorm:"type:varchar(255);not null;comment:'内容详情'"`
	Path         string `json:"path" gorm:"type:varchar(100);not null;comment:'路径'"`
	Introduction string `json:"introduction" gorm:"type:varchar(255);not null;comment:'简介'"`
	Img          string `json:"img" gorm:"type:varchar(255);not null;comment:'作品主图'"`
	Sort         int    `json:"sort" gorm:"column:sort;type:int;default:0;comment:排序"`
}

func (m *MessageIndex) TableName() string {
	return "message_index"
}
