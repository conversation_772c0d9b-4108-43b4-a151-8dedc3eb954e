package repository

import (
	"context"
	"nunu-layout-admin/internal/model"
)

type AwardRepository interface {
	GetAward(ctx context.Context, id int64) (*model.Award, error)
}

func NewAwardRepository(
	repository *Repository,
) AwardRepository {
	return &awardRepository{
		Repository: repository,
	}
}

type awardRepository struct {
	*Repository
}

func (r *awardRepository) GetAward(ctx context.Context, id int64) (*model.Award, error) {
	var award model.Award

	return &award, nil
}
