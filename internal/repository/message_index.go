package repository

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
)

type MessageIndexRepository interface {
	Create(ctx context.Context, v *v1.SaveMessageIndexRequest) error
	Update(ctx context.Context, v *v1.UpdateMessageIndexRequest) error
	Delete(ctx context.Context, id int) error
	List(ctx context.Context, req *v1.MessageIndexListRequest) (*v1.MessageIndexListResponse, error)
	Detail(ctx context.Context, id uint) (*model.MessageIndex, error)
}

func NewMessageIndexRepository(
	repository *Repository,
	// categoryRepo CategoryRepository, // 传入分类仓储实例
) MessageIndexRepository {
	return &messageIndexRepository{
		Repository: repository,
		//categoryRepo: categoryRepo, // 保存依赖
	}
}

type messageIndexRepository struct {
	*Repository
	//categoryRepo CategoryRepository // 分类仓储依赖
}

func (r *messageIndexRepository) Create(ctx context.Context, v *v1.SaveMessageIndexRequest) error {

	MessageIndex := &model.MessageIndex{
		Project:      v.Project,
		Type:         v.Type,
		Client:       v.Client,
		Year:         v.Year,
		Content:      v.Content,
		ChTitle:      v.ChTitle,
		EnTitle:      v.EnTitle,
		Path:         v.Path,
		Introduction: v.Introduction,
		Img:          v.Img,
		Sort:         v.Sort,
	}
	return r.DB(ctx).Create(MessageIndex).Error
}

func (r *messageIndexRepository) Update(ctx context.Context, v *v1.UpdateMessageIndexRequest) error {

	MessageIndex := &model.MessageIndex{
		Project:      v.Project,
		Type:         v.Type,
		Client:       v.Client,
		Year:         v.Year,
		Content:      v.Content,
		ChTitle:      v.ChTitle,
		EnTitle:      v.EnTitle,
		Path:         v.Path,
		Introduction: v.Introduction,
		Img:          v.Img,
		Sort:         v.Sort,
	}
	return r.DB(ctx).Where("id = ?", v.Id).Select("*").Updates(MessageIndex).Error
}

func (r *messageIndexRepository) Delete(ctx context.Context, id int) error {

	return r.DB(ctx).Delete(&model.MessageIndex{}, id).Error
}

// messageIndexRepository 实现列表方法
func (r *messageIndexRepository) List(ctx context.Context, req *v1.MessageIndexListRequest) (*v1.MessageIndexListResponse, error) {
	db := r.DB(ctx).Model(&model.MessageIndex{})

	// 构建查询条件
	if req.Project != "" {
		db = db.Where("project LIKE ?", "%"+req.Project+"%")
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, err
	}

	// 查询列表
	var list []*model.MessageIndex

	total, err := paginate(db, req.Page, req.PageSize, &list, "sort DESC, year DESC")
	if err != nil {
		return nil, err
	}

	// 构建响应
	resp := &v1.MessageIndexListResponse{
		Total: total,
		List:  make([]*v1.MessageIndexItem, len(list)),
	}
	// 批量获取所有需要查询的分类ID
	//typeIDSet := make(map[int64]struct{})
	//for _, item := range list {
	//	if item.Type > 0 {
	//		typeIDSet[item.Type] = struct{}{}
	//	}
	//}

	//typeIDs := make([]int64, 0)
	//for id := range typeIDSet {
	//	typeIDs = append(typeIDs, id)
	//}

	// 一次性查询所有需要的分类
	//typeMap := make(map[int64]string)
	//if len(typeIDs) > 0 {
	//	categories, err := r.categoryRepo.GetCategories(ctx, typeIDs)
	//	if err == nil {
	//		for _, cat := range categories {
	//			typeMap[int64(cat.ID)] = cat.TypeName
	//		}
	//	}
	//}

	// 转换模型为API响应格式
	for i, item := range list {
		resp.List[i] = &v1.MessageIndexItem{
			ID:           item.ID,
			Project:      item.Project,
			Type:         item.Type,
			Client:       item.Client,
			Year:         item.Year,
			Content:      item.Content,
			ChTitle:      item.ChTitle,
			EnTitle:      item.EnTitle,
			Path:         item.Path,
			Introduction: item.Introduction,
			Img:          item.Img,
			Sort:         item.Sort,
		}

		// 从缓存map中获取分类名称
		//if item.Type > 0 {
		//	if typeName, exists := typeMap[item.Type]; exists {
		//		resp.List[i].TypeName = typeName
		//	} else {
		//		resp.List[i].TypeName = "未知分类"
		//	}
		//}
	}
	return resp, nil
}

// messageIndexRepository 实现详情方法
func (r *messageIndexRepository) Detail(ctx context.Context, id uint) (*model.MessageIndex, error) {
	var item model.MessageIndex
	err := r.DB(ctx).First(&item, id).Error
	return &item, err
}
