/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AApp: typeof import('ant-design-vue/es')['App']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    AAvatarGroup: typeof import('ant-design-vue/es')['AvatarGroup']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardGrid: typeof import('ant-design-vue/es')['CardGrid']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    Access: typeof import('./../src/components/access/index.vue')['default']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AFormItemRest: typeof import('ant-design-vue/es')['FormItemRest']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutFooter: typeof import('ant-design-vue/es')['LayoutFooter']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    AStatisticCountdown: typeof import('ant-design-vue/es')['StatisticCountdown']
    AStep: typeof import('ant-design-vue/es')['Step']
    ASteps: typeof import('ant-design-vue/es')['Steps']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATableColumn: typeof import('ant-design-vue/es')['TableColumn']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimePicker: typeof import('ant-design-vue/es')['TimePicker']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    ATypographyParagraph: typeof import('ant-design-vue/es')['TypographyParagraph']
    ATypographyText: typeof import('ant-design-vue/es')['TypographyText']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    AWatermark: typeof import('ant-design-vue/es')['Watermark']
    BaseLoading: typeof import('./../src/components/base-loading/index.vue')['default']
    CarbonLanguage: typeof import('./../src/components/icons/carbon-language.vue')['default']
    CarbonMoon: typeof import('./../src/components/icons/carbon-moon.vue')['default']
    CarbonSun: typeof import('./../src/components/icons/carbon-sun.vue')['default']
    ChaseSpin: typeof import('./../src/components/base-loading/spin/chase-spin.vue')['default']
    CubeSpin: typeof import('./../src/components/base-loading/spin/cube-spin.vue')['default']
    DocLink: typeof import('./../src/components/doc-link/index.vue')['default']
    DotSpin: typeof import('./../src/components/base-loading/spin/dot-spin.vue')['default']
    FooterLinks: typeof import('./../src/components/footer-links.vue')['default']
    FooterToolBar: typeof import('./../src/components/footer-tool-bar/index.vue')['default']
    GiteeLink: typeof import('./../src/components/gitee-link/index.vue')['default']
    GithubLink: typeof import('./../src/components/github-link/index.vue')['default']
    PageContainer: typeof import('./../src/components/page-container/index.vue')['default']
    PlaneSpin: typeof import('./../src/components/base-loading/spin/plane-spin.vue')['default']
    PreloaderSpin: typeof import('./../src/components/base-loading/spin/preloader-spin.vue')['default']
    PulseSpin: typeof import('./../src/components/base-loading/spin/pulse-spin.vue')['default']
    RectSpin: typeof import('./../src/components/base-loading/spin/rect-spin.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectLang: typeof import('./../src/components/select-lang/index.vue')['default']
    TokenProvider: typeof import('./../src/components/token-provider/index.vue')['default']
    Tooltip: typeof import('ant-design-vue/es')['Tooltip']
    UserAvatar: typeof import('./../src/components/user-avatar/index.vue')['default']
    VirtualList: typeof import('./../src/components/virtual-list/index.vue')['default']
  }
}
