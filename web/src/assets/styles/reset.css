@import "./motion.css";
html {
  --text-color: rgba(0,0,0,.85);
  --text-color-1: rgba(0,0,0,.45);
  --text-color-2: rgba(0,0,0,.2);
  --bg-color: #fff;
  --hover-color:rgba(0,0,0,.025);
  --bg-color-container: #f0f2f5;
  --c-shadow: 2px 0 8px 0 rgba(29,35,41,.05);
}

html.dark{
  --text-color: rgba(229, 224, 216, 0.85);
  --text-color-1: rgba(229, 224, 216, 0.45);
  --text-color-2: rgba(229, 224, 216, 0.45);
  --bg-color: rgb(36, 37, 37);
  --hover-color:rgb(42, 44, 55);
  --bg-color-container: rgb(42, 44, 44);
  --c-shadow: rgba(13, 13, 13, 0.65) 0 2px 8px 0;
}

body{
  color: var(--text-color);
  background-color: var(--bg-color);
  text-rendering: optimizeLegibility;
  overflow: hidden;
}

#app, body, html{
  height: 100%;
}

#app{
  overflow-x: hidden;
}
*, :after, :before{
  box-sizing: border-box;
}
