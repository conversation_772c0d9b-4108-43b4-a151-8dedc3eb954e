<script setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
import { debounce } from 'lodash'
import Quill from 'quill'
import {
  createMessageIndexApi,
  createOrUpdateMessageIndexDetailApi,
  deleteMessageIndexApi,
  getMessageIndexDetail,
  getMessageIndexListApi,
  updateMessageIndexApi,
} from '~@/api/common/messageindex'

import { getSignatureApi } from '~@/api/common/messagework'

import { getCategoryListApi } from '~@/api/common/category'

// 导入富文本编辑器
import 'quill/dist/quill.snow.css' // 导入样式

// 状态管理
const loading = ref(false)

const list = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
})

const filter = reactive({
  client: '',
  project: '',
  type: '',
})

// 富文本编辑器弹窗状态
const detailModalVisible = ref(false)

// 富文本编辑器相关状态
const richTextContent = ref('')
const currentRecordId = ref(null)
const quillEditor = ref(null)
const editorContainer = ref(null)

// 富文本编辑器配置
const editorOptions = {
  theme: 'snow',
  placeholder: '请输入详情内容...',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link', 'image'],
    ],
  },
}

// 初始化富文本编辑器
function initQuillEditor () {
  // 先清理现有的编辑器实例
  if (quillEditor.value) {
    quillEditor.value = null
  }

  if (editorContainer.value) {
    // 清空容器内容，防止重复创建工具栏
    editorContainer.value.innerHTML = ''

    // 创建新的编辑器实例
    quillEditor.value = new Quill(editorContainer.value, editorOptions)

    // 监听内容变化
    quillEditor.value.on('text-change', () => {
      richTextContent.value = quillEditor.value.root.innerHTML
    })
  }
}

// 设置编辑器内容
function setEditorContent (content) {
  if (quillEditor.value) {
    quillEditor.value.root.innerHTML = content || ''
    richTextContent.value = content || ''
  }
}

// 清理编辑器
function cleanupEditor () {
  if (quillEditor.value) {
    quillEditor.value = null
  }
  if (editorContainer.value) {
    editorContainer.value.innerHTML = ''
  }
  richTextContent.value = ''
  currentRecordId.value = null
}

async function handleDetail (record) {
  try {
    currentRecordId.value = record.id

    // 获取详情数据
    const { data } = await getMessageIndexDetail(record.id)
    console.log('获取详情数据:', data)

    // 设置富文本内容，如果没有内容则为空字符串
    richTextContent.value = data.content || ''

    detailModalVisible.value = true // 打开弹窗

    // 等待DOM更新后初始化编辑器
    await nextTick()
    initQuillEditor()
    setEditorContent(data.content || '')

    console.log('富文本内容设置成功:', richTextContent.value)
  }
  catch (error) {
    console.error('请求详情接口失败', error)
    message.error('获取详情失败，请重试')
  }
}

// 富文本编辑器保存函数
async function handleRichTextSave () {
  if (!currentRecordId.value) {
    message.error('缺少作品 ID，无法保存')
    return
  }

  try {
    // 调用更新接口保存富文本内容
    await updateMessageIndexApi({
      id: currentRecordId.value,
      content: richTextContent.value,
    })

    message.success('详情内容保存成功')
    detailModalVisible.value = false
    fetchData() // 刷新主列表数据

    // 清理编辑器
    cleanupEditor()
  }
  catch (error) {
    console.error('保存详情内容失败', error)
    message.error('保存失败，请重试')
  }
}

// 模态框相关
const modalVisible = ref(false)
const modalLoading = ref(false)
const formData = ref({
  id: null,
  client: '',
  project: '',
  type: '',
  year: '',
  ch_title: '',
  en_title: '',
  path: '',
  introduction: '',
  content: '',
  img: '',
  sort: null,
})
const formRef = ref(null)

// 获取数据
async function fetchData () {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filter,
    }
    const res = await getMessageIndexListApi(params)
    list.value = res.data.list || []
    console.log('接口返回的数据:', res.data.list) // 添加日志输出接口返回的数据
    console.log('赋值给list的数据:', list.value) // 添加日志输出赋值给list的数据
    pagination.total = res.data.total || 0
  }
  catch (error) {
    console.error('获取数据失败', error)
    message.error('获取数据失败，请稍后重试')
  }
  finally {
    loading.value = false
  }
}

// 表格变化处理
function handleTableChange (paginationInfo) {
  pagination.current = paginationInfo.current
  pagination.pageSize = paginationInfo.pageSize
  fetchData()
}

// 搜索和重置
function searchData () {
  pagination.current = 1
  fetchData()
}

// 又拍云
const uploadFile = ref(null) // 临时存储上传的文件

async function handleBeforeUpload (file) {
  try {
    // 获取REST API签名信息
    const fileExt = file.name.split('.').pop() || 'jpg'
    const randomFileName = `image-${Date.now()}.${fileExt}`

    // 传递完整的saveKey路径，使用模板字符串
    const { data } = await getSignatureApi({
      save_key: `/uploads/${randomFileName}`, // 正确使用模板字符串
    })
    const { policy, authorization, bucket, save_key } = data
    const expiration = Math.floor(Date.now() / 1000) + 30 * 60
    // 构建FormData
    const formDatas = new FormData()
    formDatas.append('policy', policy) // 上传策略
    formDatas.append('authorization', authorization) // 签名
    formDatas.append('file', file)
    formDatas.append('expiration', expiration.toString())
    // 要上传的文件
    formDatas.append('save-key', save_key) // 文件保存路径

    // 发送请求到又拍云API
    const response = await fetch(`https://v0.api.upyun.com/${bucket}`, {
      method: 'POST',
      body: formDatas,
    })

    // 处理响应
    if (response.status != 200) {
      const errorData = await response.json()
      throw new Error(`上传失败 (${errorData.code}): ${errorData.message}`)
    }

    const result = await response.json()
    console.log('上传成功:', result.url)
    console.log('处理', 'dddd')
    // 更新表单数据
    formData.value.img = `//static.aoneandatwodesign.com/${result.url}`
    console.log('处理', formData.value.img)
    message.success('图片上传成功')
    return false // 阻止组件默认上传
  }
  catch (error) {
    console.log('处理', error)
    message.error('上传失败，请重试')
    return false
  }
}

function resetFilter () {
  filter.client = ''
  filter.project = ''
  filter.type = ''
  searchData()
}

// 刷新数据
function refreshData () {
  pagination.current = 1
  fetchData()
}

// 新增和编辑
function handleAdd () {
  formData.value = {
    id: null,
    client: '',
    project: '',
    type: '',
    year: '',
    path: '',
    ch_title: '',
    en_title: '',
    introduction: '',
    content: '',
    img: '',
  }
  modalVisible.value = true
}

function handleEdit (record) {
  formData.value = { ...record }
  modalVisible.value = true
}
// 上传详情图

const categoryList = ref([]) // 存储分类列表
const categoryLoading = ref(false) // 分类数据加载状态
async function fetchCategoryList () {
  categoryLoading.value = true
  try {
    const res = await getCategoryListApi({ page: 1, pageSize: 100 })
    // 映射接口数据为 { value, label } 格式
    categoryList.value = (res.data?.list || []).map(item => ({
      value: item.id, // 存储id作为value
      label: item.type_name, // 显示type_name作为标签
    }))
    console.log('处理后的分类数据：', categoryList.value) // 新增日志
  }
  catch (error) {
    console.error('获取分类数据失败', error)
    message.error('分类数据加载失败，请稍后重试')
  }
  finally {
    categoryLoading.value = false
  }
}

async function handleModalOk () {
  if (!formRef.value)
    return

  try {
    await formRef.value.validate()
    modalLoading.value = true
    console.log('formRef', formRef.value)
    if (formData.value.id) {
      const editedData = {
        id: Number.parseInt(formData.value.id, 10),
        client: formData.value.client,
        project: formData.value.project,
        type: formData.value.type,
        year: Number.parseInt(formData.value.year, 10),
        ch_title: formData.value.ch_title,
        en_title: formData.value.en_title,
        path: formData.value.path,
        content: formData.value.content,
        introduction: formData.value.introduction,
        img: formData.value.img,
        sort: Number.parseInt(formData.value.sort, 10),
      }

      // 更新
      await updateMessageIndexApi(editedData)
      message.success('更新成功')
    }
    else {
      const addData = {
        client: formData.value.client,
        project: formData.value.project,
        type: formData.value.type,
        year: Number.parseInt(formData.value.year, 10), // 将year转换为int类型
        ch_title: formData.value.ch_title,
        en_title: formData.value.en_title,
        path: formData.value.path,
        content: formData.value.content,
        introduction: formData.value.introduction,
        img: formData.value.img,
        sort: Number.parseInt(formData.value.sort, 10),
      }
      // 新增
      await createMessageIndexApi(addData)
      message.success('新增成功')
    }

    modalVisible.value = false
    fetchData()
  }
  catch (error) {
    console.error('表单验证失败', error)
  }
  finally {
    modalLoading.value = false
  }
}

function handleModalCancel () {
  modalVisible.value = false
}

// 删除
async function handleDelete (id) {
  try {
    console.log('删除失败', id)
    // const deleteData = {
    //   id:id
    // }
    await deleteMessageIndexApi({ id })
    message.success('删除成功')
    fetchData()
  }
  catch (error) {
    console.error('删除失败', error)
    message.error('删除失败，请稍后重试')
  }
}

// 删除图片
function handleRemoveImage () {
  formData.value.img = ''
}

// 生命周期钩子
onMounted(async () => {
  await fetchData()
  await fetchCategoryList() // 新增分类数据加载
})
</script>

<template>
  <div class="message-index-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">
        作品管理
      </h1>
      <div class="flex space-x-2">
        <a-button type="primary" @click="handleAdd">
          <span class="flex items-center">
            <i class="fa fa-plus mr-2" />新增
          </span>
        </a-button>
        <a-button @click="refreshData">
          <span class="flex items-center">
            <i class="fa fa-refresh mr-2" />刷新
          </span>
        </a-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-mask flex items-center justify-center h-[300px]">
      <div class="flex flex-col items-center">
        <a-spin size="large" />
        <p class="mt-3 text-gray-500">
          数据加载中，请稍候...
        </p>
      </div>
    </div>

    <!-- 列表内容区 -->
    <div v-else class="content-container">
      <!-- Ant Design风格表格 -->
      <a-table :row-key="(item) => item.id" :data-source="list" :pagination="pagination" :loading="loading" bordered
        class="shadow-sm rounded-lg overflow-hidden" @change="handleTableChange">
        <a-table-column title="序号" width="80">
          <template #default="{ index }">
            {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="client" data-index="client" width="200" />
        <a-table-column title="project" data-index="project" width="200" />
        <a-table-column title="type" data-index="type" width="120" />
        <a-table-column title="year" data-index="year" width="120" />
        <a-table-column title="排序" data-index="sort" width="120" />

        <a-table-column title="操作" width="160">
          <template #default="{ record }">
            <div class="flex space-x-2">
              <a-button type="link" size="small" @click="handleEdit(record)">
                <i class="fa fa-edit mr-1" />编辑
              </a-button>
              <a-button type="link" size="small" @click="handleDetail(record)">
                <i class="fa fa-edit mr-1" />详情图
              </a-button>
              <a-button type="link" size="small" @click="handleDelete(record.id)">
                <i class="fa fa-edit mr-1" />删除
              </a-button>
            </div>
          </template>
        </a-table-column>
      </a-table>

      <!-- 空状态 -->
      <div v-if="list.length === 0" class="empty-state flex flex-col items-center justify-center py-20">
        <p class="text-gray-500 text-lg">
          暂无数据
        </p>
        <a-button type="primary" class="mt-4" @click="refreshData">
          <i class="fa fa-refresh mr-2" />刷新数据
        </a-button>
      </div>
    </div>

    <!-- 编辑/新增模态框 -->
    <a-modal :open="modalVisible" :title="formData.id ? '编辑数据' : '新增数据'" :confirm-loading="modalLoading"
      @ok="handleModalOk" @cancel="handleModalCancel">
      <a-form ref="formRef" :model="formData" class="space-y-4" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="client">
          <a-input v-model:value="formData.client" placeholder="请输入client" />
        </a-form-item>
        <a-form-item label="project">
          <a-input v-model:value="formData.project" placeholder="请输入project" />
        </a-form-item>
        <a-form-item label="type">
          <a-input v-model:value="formData.type" placeholder="请输入type,多个需要逗号分隔" />
        </a-form-item>

        <a-form-item label="Year">
          <a-input v-model:value="formData.year" placeholder="请输入 Year" />
        </a-form-item>
        <a-form-item label="中文标签">
          <a-input v-model:value="formData.ch_title" placeholder="请输入中文标签" />
        </a-form-item>
        <a-form-item label="英文标签">
          <a-input v-model:value="formData.en_title" placeholder="请输入英文标签" />
        </a-form-item>
        <a-form-item label="详情">
          <a-textarea v-model:value="formData.content" placeholder="请输入简介" :rows="4" />
        </a-form-item>
        <a-form-item label="简介">
          <a-textarea v-model:value="formData.introduction" placeholder="请输入简介" :rows="4" />
        </a-form-item>
        <a-form-item label="排序">
          <a-input v-model:value="formData.sort" placeholder="请输入排序" />
        </a-form-item>

        <!-- 新增：图片上传区域 -->
        <a-form-item label="图片">
          <a-upload :before-upload="handleBeforeUpload" :file-list="formData.img
            ? [
              {
                uid: '-1',
                name: 'image.jpg',
                status: 'done',
                url: formData.img,
              },
            ]
            : []
            " :show-upload-list="{ show: true, showRemoveIcon: true }" class="mb-3" @remove="handleRemoveImage">
            <a-button type="primary">
              <i class="fa fa-upload mr-1" /> 选择图片
            </a-button>
          </a-upload>

          <div v-if="formData.img" class="mt-2">
            <img :src="formData.img" alt="预览图"
              class="w-40 h-40 object-cover rounded-md border border-gray-200 shadow-sm">
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 富文本编辑器弹窗 -->
    <a-modal :open="detailModalVisible" title="编辑详情内容" width="80%" :confirm-loading="false" @ok="handleRichTextSave"
      @cancel="() => { detailModalVisible = false; cleanupEditor(); }">
      <div class="p-4">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            详情内容
          </label>
          <div ref="editorContainer" style="height: 400px;" />
        </div>

        <div class="mt-4 text-sm text-gray-500">
          <p>提示：您可以在上方编辑器中输入富文本内容，支持文字格式化、插入链接、图片等功能。</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped></style>
