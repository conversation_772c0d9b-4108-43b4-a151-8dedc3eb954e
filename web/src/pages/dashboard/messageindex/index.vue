<template>
  <div class="message-index-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">作品管理</h1>
      <div class="flex space-x-2">
        <a-button type="primary" @click="handleAdd">
          <span class="flex items-center">
            <i class="fa fa-plus mr-2"></i>新增
          </span>
        </a-button>
        <a-button @click="refreshData">
          <span class="flex items-center">
            <i class="fa fa-refresh mr-2"></i>刷新
          </span>
        </a-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="loading-mask flex items-center justify-center h-[300px]"
    >
      <div class="flex flex-col items-center">
        <a-spin size="large" />
        <p class="mt-3 text-gray-500">数据加载中，请稍候...</p>
      </div>
    </div>

    <!-- 列表内容区 -->
    <div v-else class="content-container">
      <!-- Ant Design风格表格 -->
      <a-table
        :row-key="(item) => item.id"
        :data-source="list"
        :pagination="pagination"
        :loading="loading"
        bordered
        class="shadow-sm rounded-lg overflow-hidden"
        @change="handleTableChange"
      >
        <a-table-column title="序号" width="80">
          <template #default="{ index }">
            {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="client" dataIndex="client" width="200" />
        <a-table-column title="project" dataIndex="project" width="200" />
        <a-table-column title="type" dataIndex="type" width="120" />
        <a-table-column title="year" dataIndex="year" width="120" />
         <a-table-column title="排序" dataIndex="sort" width="120" />

        <a-table-column title="操作" width="160">
          <template #default="{ record }">
            <div class="flex space-x-2">
              <a-button type="link" size="small" @click="handleEdit(record)">
                <i class="fa fa-edit mr-1"></i>编辑
              </a-button>
              <a-button type="link" size="small" @click="handleDetail(record)">
                <i class="fa fa-edit mr-1"></i>详情图
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="handleDelete(record.id)"
              >
                <i class="fa fa-edit mr-1"></i>删除
              </a-button>
            </div>
          </template>
        </a-table-column>
      </a-table>

      <!-- 空状态 -->
      <div
        v-if="list.length === 0"
        class="empty-state flex flex-col items-center justify-center py-20"
      >
        <p class="text-gray-500 text-lg">暂无数据</p>
        <a-button type="primary" class="mt-4" @click="refreshData">
          <i class="fa fa-refresh mr-2"></i>刷新数据
        </a-button>
      </div>
    </div>

    <!-- 编辑/新增模态框 -->
    <a-modal
      :open="modalVisible"
      :title="formData.id ? '编辑数据' : '新增数据'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirm-loading="modalLoading"
    >
      <a-form
        :model="formData"
        ref="formRef"
        class="space-y-4"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="client">
          <a-input v-model:value="formData.client" placeholder="请输入client" />
        </a-form-item>
        <a-form-item label="project">
          <a-input
            v-model:value="formData.project"
            placeholder="请输入project"
          />
        </a-form-item>
         <a-form-item label="type">
          <a-input
            v-model:value="formData.type"
            placeholder="请输入type,多个需要逗号分隔"
          />
        </a-form-item>
      
        <a-form-item label="Year">
          <a-input v-model:value="formData.year" placeholder="请输入 Year" />
        </a-form-item>
        <a-form-item label="中文标签">
          <a-input
            v-model:value="formData.ch_title"
            placeholder="请输入中文标签"
          />
        </a-form-item>
        <a-form-item label="英文标签">
          <a-input
            v-model:value="formData.en_title"
            placeholder="请输入英文标签"
          />
        </a-form-item>
        <a-form-item label="详情">
          <a-textarea
            v-model:value="formData.content"
            placeholder="请输入简介"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="简介">
          <a-textarea
            v-model:value="formData.introduction"
            placeholder="请输入简介"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="排序">
          <a-input
            v-model:value="formData.sort"
            placeholder="请输入排序"
          />
        </a-form-item>

        <!-- 新增：图片上传区域 -->
        <a-form-item label="图片">
          <a-upload
            :before-upload="handleBeforeUpload"
            :file-list="
              formData.img
                ? [
                    {
                      uid: '-1',
                      name: 'image.jpg',
                      status: 'done',
                      url: formData.img,
                    },
                  ]
                : []
            "
            :show-upload-list="{ show: true, showRemoveIcon: true }"
            @remove="handleRemoveImage"
            class="mb-3"
          >
            <a-button type="primary">
              <i class="fa fa-upload mr-1"></i> 选择图片
            </a-button>
          </a-upload>

          <div v-if="formData.img" class="mt-2">
            <img
              :src="formData.img"
              alt="预览图"
              class="w-40 h-40 object-cover rounded-md border border-gray-200 shadow-sm"
            />
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
<!-- 详情图弹窗 -->
<a-modal
  :open="detailModalVisible"
  title="作品详情图"
  @ok="handleDetailSave"
  @cancel="detailModalVisible = false"
  width="50%"
  :footer="detailFooter"
>
  <div class="p-4">
    <!-- 详情图上传区域 -->
    <a-form-item>
      <a-upload
        :before-upload="handleDetailImageUpload"
        :file-list="detailFileList"
        :show-upload-list="{ showPreviewIcon: true, showRemoveIcon: true, showDownloadIcon: false }"
        @remove="handleDetailRemoveImage"
        multiple
        class="mb-3"
      >
        <a-button type="primary">
          <i class="fa fa-upload mr-1"></i> 上传详情图
        </a-button>
        <template #itemRender="{ file }">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <a-progress
                v-if="file.status === 'uploading'"
                :percent="file.percent"
                size="small"
                :showInfo="false"
                class="w-24 mr-2"
              />
              <span v-if="file.status === 'done'" class="text-green-500">
                <i class="fa fa-check"></i>
              </span>
              <span v-else-if="file.status === 'error'" class="text-red-500">
                <i class="fa fa-times"></i>
              </span>
            </div>
          </div>
        </template>
      </a-upload>
    </a-form-item>

    <!-- 统一的图片展示区域 -->
    <div
      v-if="detailFileList.length === 0 && sortedImages.length === 0"
      class="flex flex-col items-center justify-center py-10"
    >
      <div class="text-gray-500">暂无详情图片</div>
    </div>

    <div v-else class="grid grid-cols-4 gap-4 max-h-[60vh] overflow-y-auto">
      <!-- 新上传的图片 -->
      <div
        v-for="item in detailFileList"
        :key="item.uid"
        class="relative group"
      >
        <img
          :src="item.url"
          alt="详情图"
          class="w-full h-32 object-cover rounded-md border border-gray-200"
        />
        <!-- 默认显示排序值 -->
        <div class="absolute inset-0 flex items-center justify-center">
          <span class="text-white text-lg font-bold bg-black/50 px-3 py-1 rounded group-hover:opacity-0 transition-opacity">
            {{ item.sort }}
          </span>
        </div>
        <!-- 悬停时显示的控制面板 -->
        <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col justify-between p-2">
          <div class="flex items-center">
            <span class="text-white mr-2">排序:</span>
            <a-input-number
              v-model:value="item.sort"
              :min="0"
              size="small"
              class="w-20"
            />
          </div>
          <a-button
            type="primary"
            danger
            size="small"
            @click="() => handleDetailRemoveImage(item)"
            class="self-end"
          >
            删除
          </a-button>
        </div>
      </div>

      <!-- 已有的图片 -->
      <div
        v-for="item in sortedImages"
        :key="item.img"
        class="relative group"
      >
        <img
          :src="item.img"
          alt="详情图"
          class="w-full h-32 object-cover rounded-md border border-gray-200"
        />
        <!-- 默认显示排序值 -->
        <div class="absolute inset-0 flex items-center justify-center">
          <span class="text-white text-lg font-bold bg-black/50 px-3 py-1 rounded group-hover:opacity-0 transition-opacity">
            {{ item.sort }}
          </span>
        </div>
        <!-- 悬停时显示的控制面板 -->
        <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col justify-between p-2">
          <div class="flex items-center">
            <span class="text-white mr-2">排序:</span>
            <a-input-number
              v-model:value="item.sort"
              :min="0"
              size="small"
              class="w-20"
            />
          </div>
          <a-button
            type="primary"
            danger
            size="small"
            @click="() => handleExistingImageRemove(item)"
            class="self-end"
          >
            删除
          </a-button>
        </div>
      </div>
    </div>
  </div>
</a-modal>










<!-- 详情图弹窗 -->
<a-modal
  :open="detailModalVisible"
  title="作品详情图"
  @ok="handleDetailSave"
  @cancel="detailModalVisible = false"
  width="70%"
  :footer="detailFooter"
  class="detail-modal"
>
  <div class="p-6">
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-800 mb-4">上传新详情图</h3>
      <!-- 详情图上传区域 -->
      <a-upload
       multiple
        :before-upload="handleDetailImageUpload"
        :file-list="detailFileList"
        :show-upload-list="{ show: true, showRemoveIcon: true }"
        @remove="handleDetailRemoveImage"
        class="flex items-center"
      >
        <a-button type="primary" class="flex items-center px-4 py-2 rounded-lg bg-primary hover:bg-primary/90 transition-colors">
          <i class="fa fa-cloud-upload mr-2"></i>上传详情图
        </a-button>
      </a-upload>
    </div>

    <!-- 临时上传的图片预览 -->
    <div v-if="detailFileList.length > 0" class="mb-8">
      <h3 class="text-lg font-medium text-gray-800 mb-4">待保存的图片</h3>
      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        <div v-for="item in detailFileList" :key="item.uid" class="group relative">
          <img
            :src="item.url"
            alt="预览图"
            class="w-full h-40 object-cover rounded-lg border border-gray-200 shadow-sm transition-transform duration-300 group-hover:scale-105"
          >
          <div class="absolute inset-0 bg-black/60 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <a-button
              type="link"
              size="small"
              @click="() => handleDetailRemoveImage(item)"
              class="text-white bg-red-500 hover:bg-red-600 px-3 py-1 rounded transition-colors"
            >
              <i class="fa fa-trash mr-1"></i>删除
            </a-button>
          </div>
          <div class="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
            <a-input
              v-model:value="item.sort"
              size="small"
              placeholder="排序"
              class="w-16 bg-transparent text-white border border-gray-500 rounded px-2 py-1 text-xs"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 已保存的详情图列表 -->
    <div>
      <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
        <i class="fa fa-images mr-2 text-primary"></i>已保存的详情图
        <span class="ml-2 text-sm text-gray-500">(按排序值降序排列,修改排序后请点击右下角确定按钮得以生效)</span>
      </h3>
      
      <div v-if="sortedImages.length === 0 && detailFileList.length === 0" class="flex flex-col items-center justify-center py-16">
        <div class="text-gray-400 text-5xl mb-3"><i class="fa fa-image"></i></div>
        <p class="text-gray-500 text-lg mb-4">暂无详情图片</p>
       <!-- <a-button type="primary" @click="() => $refs.uploadRef.click()" class="px-4 py-2 rounded-lg bg-primary hover:bg-primary/90 transition-colors">
          <i class="fa fa-upload mr-2"></i>上传图片
        </a-button>-->
      </div>

      <div v-else class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        <!-- 展示已有详情图（带可编辑排序） -->
        <div v-for="(item, index) in sortedImages" :key="item.img" class="group relative">
          <img
            :src="item.img"
            alt="详情图"
            class="w-full h-40 object-cover rounded-lg border border-gray-200 shadow-sm transition-transform duration-300 group-hover:scale-105"
          >
          <div class="absolute inset-0 bg-black/60 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <a-button
              type="link"
              size="small"
              @click="() => handleDeleteDetailImage(item)"
              class="text-white bg-red-500 hover:bg-red-600 px-3 py-1 rounded transition-colors"
            >
              <i class="fa fa-trash mr-1"></i>删除
            </a-button>
          </div>
          <div class="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
            <a-input
              v-model:value="item.sort"
              size="small"
              placeholder="排序"
              class="w-16 bg-transparent text-white border border-gray-500 rounded px-2 py-1 text-xs"
            />
          </div>
        </div>
      </div>
    </div>
  </div>


</a-modal>




























  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { message } from "ant-design-vue";
import {
  getMessageIndexListApi,
  createMessageIndexApi,
  updateMessageIndexApi,
  deleteMessageIndexApi,
  getMessageIndexDetail,
  createOrUpdateMessageIndexDetailApi,
} from "~@/api/common/messageindex";

import { getSignatureApi } from "~@/api/common/messagework";

import { getCategoryListApi } from "~@/api/common/category";
import { debounce } from 'lodash';

// 删除已有详情图
const handleDeleteDetailImage = (item) => {
  // 直接从 bannerConfig.img_arr 中移除图片
  bannerConfig.value.img_arr = bannerConfig.value.img_arr.filter(
    (imgItem) => imgItem.img !== item.img
  );
  message.success("图片已删除");
};

// 状态管理
const loading = ref(false);
const list = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "50", "100"],
});
// 删除逻辑（仅前端清空）
const handleRemoveImage = () => {
  formData.value.img = "";
};
const filter = reactive({
  client: "",
  project: "",
  type: "",
});
//详情图
const bannerConfig = ref({
  index_id: null,
  img_arr: [],
});

// 详情图弹窗状态
const detailModalVisible = ref(false);


// const sortedImages = computed(() => {
//   // 对bannerConfig中的img_arr按sort降序排列
//   return [...bannerConfig.value.img_arr].sort((a, b) => b.sort - a.sort);
// });
// 取消排序，直接返回原始数组顺序
const sortedImages = computed(() => bannerConfig.value.img_arr);


const handleDetail = async (record) => {
  try {
    const { data } = await getMessageIndexDetail(record.id);
    console.log("data", data.index_id);

    // 直接解构赋值，无论img_arr是否为空
    bannerConfig.value = {
      index_id: data.index_id,
      img_arr: data.img_arr,
    };
    detailModalVisible.value = true; // 打开弹窗
    console.log("详情数据赋值成功", bannerConfig.value);
  } catch (error) {
    console.error("请求详情接口失败", error);
    // 静默失败，不显示错误提示
  }
};

// 详情图弹窗专用的上传状态（避免与主表单冲突）
const detailFileList = ref([]); // 临时存储上传的详情图片
const detailImageLoading = ref(false); // 详情图上传加载状态

// 复用编辑弹窗的 handleBeforeUpload 逻辑，但调整保存路径和数据绑定
// const handleDetailImageUpload = async (file) => {
//   try {
//     // 生成详情图专属文件名（例如添加 `detail-` 前缀）
//     const fileExt = file.name.split(".").pop() || "jpg";
//     const randomFileName = `detail-${Date.now()}.${fileExt}`;

//     // 调用同一签名接口，但指定详情图保存路径（如 `/details/` 目录）
//     const { data } = await getSignatureApi({
//       save_key: `/details/${randomFileName}`, // 与主图路径区分
//     });

//     // 复用编辑弹窗的上传请求逻辑（完全相同）
//     const formDatas = new FormData();
//     formDatas.append("policy", data.policy);
//     formDatas.append("authorization", data.authorization);
//     formDatas.append("file", file);
//     formDatas.append("expiration", Math.floor(Date.now() / 1000) + 30 * 60);
//     formDatas.append("save-key", data.save_key); // 注意参数名是 save-key

//     const response = await fetch(`https://v0.api.upyun.com/${data.bucket}`, {
//       method: "POST",
//       body: formDatas,
//     });
//     if (!response.ok) throw new Error("上传失败");

//     const result = await response.json();
//     const imageUrl = `//static.aoneandatwodesign.com/${result.url}`; // 复用域名拼接

//     // 将上传成功的图片添加到详情图临时列表（需包含 sort 字段，默认 0）
//     detailFileList.value = [
//       ...detailFileList.value,
//       {
//         uid: file.uid,
//         name: file.name,
//         status: "done",
//         url: imageUrl,
//         sort: 0, // 初始排序为 0，后续可通过接口调整
//       },
//     ];
//     message.success("详情图上传成功");
//     return false; // 阻止组件默认上传
//   } catch (error) {
//     message.error("上传失败，请重试");
//     return false;
//   }
// };






// 上传队列
const uploadQueue = ref([]);
const isUploading = ref(false);

// 添加到上传队列
const enqueueUpload = (file) => {
  uploadQueue.value.push(file);
  processUploadQueue();
};

// 处理上传队列
const processUploadQueue = async () => {
  if (isUploading.value || uploadQueue.value.length === 0) return;
  
  isUploading.value = true;
  const file = uploadQueue.value.shift();
  
  try {
    // 原有的上传逻辑
    const fileExt = file.name.split(".").pop() || "jpg";
    const randomFileName = `detail-${Date.now()}.${fileExt}`;
    
    const { data } = await getSignatureApi({
      save_key: `/details/${randomFileName}`,
    });
    
    const formDatas = new FormData();
    formDatas.append("policy", data.policy);
    formDatas.append("authorization", data.authorization);
    formDatas.append("file", file);
    formDatas.append("expiration", Math.floor(Date.now() / 1000) + 30 * 60);
    formDatas.append("save-key", data.save_key);
    
    const response = await fetch(`https://v0.api.upyun.com/${data.bucket}`, {
      method: "POST",
      body: formDatas,
    });
    
    if (!response.ok) throw new Error("上传失败");
    
    const result = await response.json();
    const imageUrl = `//static.aoneandatwodesign.com/${result.url}`;
    
    detailFileList.value = [
      ...detailFileList.value,
      {
        uid: file.uid,
        name: file.name,
        status: "done",
        url: imageUrl,
        sort: 0,
      },
    ];
    message.success("详情图上传成功");
  } catch (error) {
    message.error("上传失败，请重试");
  } finally {
    isUploading.value = false;
    processUploadQueue(); // 继续处理下一个
  }
};

// 修改 handleDetailImageUpload 方法
const handleDetailImageUpload = (file) => {
  enqueueUpload(file);
  return false; // 阻止组件默认上传
};






// 详情图删除逻辑（仅移除临时列表中的项，不影响已保存数据）
const handleDetailRemoveImage = (file) => {
  detailFileList.value = detailFileList.value.filter(
    (item) => item.uid !== file.uid
  );
};



const handleDetailSave = async () => {
  if (!bannerConfig.value.index_id) {
    message.error("缺少作品 ID，无法保存");
    return;
  }

  try {
    // 1. 处理已保存图片的排序修改（直接使用用户输入的值）
    const updatedImages = bannerConfig.value.img_arr.map((item) => ({
      img: item.img,
      sort: parseInt(item.sort) || 0, // 使用用户输入的排序值
    }));

    // 2. 处理新上传图片的排序（使用用户输入的值或自动生成）
    const newUploadedImages = detailFileList.value.map((item) => {
      const userSort = parseInt(item.sort);
      return {
        img: item.url,
        // 优先使用用户输入的排序值，否则生成一个比当前最大排序大1的值
        sort: !isNaN(userSort) && userSort > 0 ? userSort : (sortedImages.length > 0 ? Math.max(...sortedImages.map((i) => i.sort)) + 1 : 1),
      };
    });

    // 3. 合并并按排序值降序排列（确保服务器接收正确顺序）
    const allImages = [...updatedImages, ...newUploadedImages].sort((a, b) => b.sort - a.sort);

    // 4. 调用接口提交详情图数据
    await createOrUpdateMessageIndexDetailApi({
      index_id: bannerConfig.value.index_id,
      img_arr: allImages,
    });

    message.success("详情图保存成功");
    detailModalVisible.value = false;
    fetchData(); // 刷新主列表数据
    detailFileList.value = []; // 清空临时列表
  } catch (error) {
    console.error("保存详情图失败", error);
    message.error("保存失败，请重试");
  }
};





// 模态框相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const formData = ref({
  id: null,
  client: "",
  project: "",
  type: "",
  year: "",
  ch_title: "",
  en_title: "",
  path: "",
  introduction: "",
  content: "",
  img: "",
  sort:null
});
const formRef = ref(null);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filter,
    };
    const res = await getMessageIndexListApi(params);
    list.value = res.data.list || [];
    console.log("接口返回的数据:", res.data.list); // 添加日志输出接口返回的数据
    console.log("赋值给list的数据:", list.value); // 添加日志输出赋值给list的数据
    pagination.total = res.data.total || 0;
  } catch (error) {
    console.error("获取数据失败", error);
    message.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (paginationInfo) => {
  pagination.current = paginationInfo.current;
  pagination.pageSize = paginationInfo.pageSize;
  fetchData();
};

// 搜索和重置
const searchData = () => {
  pagination.current = 1;
  fetchData();
};

//又拍云
const uploadFile = ref(null); // 临时存储上传的文件

const handleBeforeUpload = async (file) => {
  try {
    // 获取REST API签名信息
    const fileExt = file.name.split(".").pop() || "jpg";
    const randomFileName = `image-${Date.now()}.${fileExt}`;

    // 传递完整的saveKey路径，使用模板字符串
    const { data } = await getSignatureApi({
      save_key: `/uploads/${randomFileName}`, // 正确使用模板字符串
    });
    const { policy, authorization, bucket, save_key } = data;
    const expiration = Math.floor(Date.now() / 1000) + 30 * 60;
    // 构建FormData
    const formDatas = new FormData();
    formDatas.append("policy", policy); // 上传策略
    formDatas.append("authorization", authorization); // 签名
    formDatas.append("file", file);
    formDatas.append("expiration", expiration.toString());
    // 要上传的文件
    formDatas.append("save-key", save_key); // 文件保存路径

    // 发送请求到又拍云API
    const response = await fetch(`https://v0.api.upyun.com/${bucket}`, {
      method: "POST",
      body: formDatas,
    });

    // 处理响应
    if (response.status != 200) {
      const errorData = await response.json();
      throw new Error(`上传失败 (${errorData.code}): ${errorData.message}`);
    }

    const result = await response.json();
    console.log("上传成功:", result.url);
    console.log("处理", "dddd");
    // 更新表单数据
    formData.value.img = `//static.aoneandatwodesign.com/${result.url}`;
    console.log("处理", formData.value.img);
    message.success("图片上传成功");
    return false; // 阻止组件默认上传
  } catch (error) {
    console.log("处理", error);
    message.error("上传失败，请重试");
    return false;
  }
};

const resetFilter = () => {
  filter.client = "";
  filter.project = "";
  filter.type = "";
  searchData();
};

// 刷新数据
const refreshData = () => {
  pagination.current = 1;
  fetchData();
};

// 新增和编辑
const handleAdd = () => {
  formData.value = {
    id: null,
    client: "",
    project: "",
    type: "",
    year: "",
    path: "",
    ch_title: "",
    en_title: "",
    introduction: "",
    content: "",
    img: "",
  };
  modalVisible.value = true;
};

const handleEdit = (record) => {
  formData.value = { ...record };
  modalVisible.value = true;
};
//上传详情图

const categoryList = ref([]); // 存储分类列表
const categoryLoading = ref(false); // 分类数据加载状态
const fetchCategoryList = async () => {
  categoryLoading.value = true;
  try {
    const res = await getCategoryListApi({ page: 1, pageSize: 100 });
    // 映射接口数据为 { value, label } 格式
    categoryList.value = (res.data?.list || []).map((item) => ({
      value: item.id, // 存储id作为value
      label: item.type_name, // 显示type_name作为标签
    }));
    console.log("处理后的分类数据：", categoryList.value); // 新增日志
  } catch (error) {
    console.error("获取分类数据失败", error);
    message.error("分类数据加载失败，请稍后重试");
  } finally {
    categoryLoading.value = false;
  }
};

const handleModalOk = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    modalLoading.value = true;
    console.log("formRef", formRef.value);
    if (formData.value.id) {
      const editedData = {
        id: parseInt(formData.value.id, 10),
        client: formData.value.client,
        project: formData.value.project,
        type: formData.value.type,
        year: parseInt(formData.value.year, 10),
        ch_title: formData.value.ch_title,
        en_title: formData.value.en_title,
        path: formData.value.path,
        content: formData.value.content,
        introduction: formData.value.introduction,
        img: formData.value.img,
        sort:parseInt(formData.value.sort,10),
      };

      // 更新
      await updateMessageIndexApi(editedData);
      message.success("更新成功");
    } else {
      const addData = {
        client: formData.value.client,
        project: formData.value.project,
        type: formData.value.type,
        year: parseInt(formData.value.year, 10), // 将year转换为int类型
        ch_title: formData.value.ch_title,
        en_title: formData.value.en_title,
        path: formData.value.path,
        content: formData.value.content,
        introduction: formData.value.introduction,
        img: formData.value.img,
        sort:parseInt(formData.value.sort,10),
      };
      // 新增
      await createMessageIndexApi(addData);
      message.success("新增成功");
    }

    modalVisible.value = false;
    fetchData();
  } catch (error) {
    console.error("表单验证失败", error);
  } finally {
    modalLoading.value = false;
  }
};

const handleModalCancel = () => {
  modalVisible.value = false;
};

// 删除
const handleDelete = async (id) => {
  try {
    console.log("删除失败", id);
    // const deleteData = {
    //   id:id
    // }
    await deleteMessageIndexApi({ id: id });
    message.success("删除成功");
    fetchData();
  } catch (error) {
    console.error("删除失败", error);
    message.error("删除失败，请稍后重试");
  }
};

// 删除已有图片
const handleExistingImageRemove = (image) => {
  // 从 bannerConfig 中移除指定图片
  bannerConfig.value.img_arr = bannerConfig.value.img_arr.filter(
    (item) => item.img !== image.img
  );
  message.success('图片已移除');
};


// 生命周期钩子
onMounted(async () => {
  await fetchData();
  await fetchCategoryList(); // 新增分类数据加载
});
</script>

<style scoped></style>
