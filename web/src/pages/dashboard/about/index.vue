<script setup>
defineOptions({
  name: 'BasicForm',
})

import { ref, reactive,onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {

  getSignatureApi,
} from "~@/api/common/messagework";

import{
  getAboutDetailApi,
  updateAboutApi,
  createAboutApi
} from "~@/api/common/about"




// 新增：获取关于我们数据
// 页面加载时获取数据（修改：移除loading状态）
onMounted(() => {
  getAboutDetailApi()
    .then(response => {
      console.log('获取关于我们数据:', response);
      if (response.code === 0 && response.data) {
        Object.assign(formState, response.data);
      }
      console.log("赋值后的数据",formState)
    })
    .catch(error => {
       console.log("赋值后的数据",formState)
      console.error('获取数据失败:', error);
    });
});

// 响应式数据
const formState = reactive({
  id:null,
  ch_title: '',    // 中文标题
  en_title: '',    // 英文标题
  email: '',        // 邮箱
  address: '',      // 地址
  follow_us: '',     // 关注我们
  img: '',          // 图片URL
});

const handleRemoveFile = () => {
  // console.log("dddd",formRef,formState.value)
  formState.img = ''
}

// 表单引用
const formRef = ref();

// 上传中的状态
const uploading = ref(false);

// 提交处理函数
// async function handleSubmit() {
//   try {
//     // 验证并获取表单值
//     const values = await formRef.value?.validateFields();
//     console.log('提交成功:', values);
    
//     // 这里可添加实际提交逻辑（如调用接口）
//     // 示例：假设这里调用API提交表单数据
//     // await submitFormApi(values);
//     message.success('表单提交成功');
    
//   } catch (errorInfo) {
//     console.log('验证失败:', errorInfo);
//     message.error('请检查表单信息');
//   }
// }

// 修改：提交处理函数（新增API调用逻辑）
// 提交处理函数
async function handleSubmit() {
  try {
    const values = await formRef.value?.validateFields();
    console.log('提交数据:', values);
    
    if (formState.id) {
      await updateAboutApi({
      ...values,
      id: formState.id
    });
      message.success('更新成功');
    } else {
      await createAboutApi(values);
      message.success('创建成功');
    }
  } catch (error) {
    console.log('提交失败:', error);
    message.error('提交失败，请检查表单信息');
  }
}

const handleBeforeUpload = async (file) => {
  try {
    // 获取REST API签名信息
     const fileExt = file.name.split('.').pop() || 'jpg';
    const randomFileName = `image-${Date.now()}.${fileExt}`;
    
    // 传递完整的saveKey路径，使用模板字符串
    const { data } = await getSignatureApi({ 
      save_key: `/uploads/${randomFileName}` // 正确使用模板字符串
    });
    const { policy, authorization, bucket, save_key } = data;
    const expiration = Math.floor(Date.now() / 1000) + 30 * 60;
    // 构建FormData
    const formDatas = new FormData();
    formDatas.append("policy", policy);        // 上传策略
    formDatas.append("authorization", authorization); // 签名
    formDatas.append("file", file);
    formDatas.append("expiration",  expiration.toString());
              // 要上传的文件
    formDatas.append("save-key", save_key);    // 文件保存路径
    
    // 发送请求到又拍云API
    const response = await fetch(`https://v0.api.upyun.com/${bucket}`, {
      method: "POST",
      body: formDatas,
    });

    // 处理响应
    if (response.status != 200) {
      const errorData = await response.json();
      throw new Error(`上传失败 (${errorData.code}): ${errorData.message}`);
    }
    
    const result = await response.json();
    console.log("上传成功:", result.url);
     console.log("处理","dddd")
    // 更新表单数据
    formState.img = `//static.aoneandatwodesign.com/${result.url}`;
    message.success("图片上传成功");
    return false; // 阻止组件默认上传
  } catch (error) {
    console.log("处理",error)
    message.error("上传失败，请重试");
    return false;
  }
};
</script>

<template>
  <page-container>
    <a-card :body-style="{ padding: '24px 32px' }" :bordered="false">
      <a-form ref="formRef" :model="formState">
        <!-- 中文标题 -->
        
        <a-form-item
          name="ch_title"
          label="中文标题"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{ required: true, message: '请填写中文标题' }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            v-model:value="formState.ch_title"
            placeholder="请输入中文标题"
          />
        </a-form-item>

        <!-- 英文标题 -->
        <a-form-item
          name="en_title"
          label="英文标题"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{ required: true, message: '请填写英文标题' }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            v-model:value="formState.en_title"
            placeholder="请输入英文标题"
          />
        </a-form-item>

        <!-- 邮箱 -->
        <a-form-item
          name="email"
          label="邮箱"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{
            required: true,
            message: '请填写邮箱',
            type: 'email',
            pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
            message: '请输入正确的邮箱格式'
          }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            v-model:value="formState.email"
            placeholder="请输入邮箱"
            type="email"
          />
        </a-form-item>

        <!-- 地址 -->
        <a-form-item
          name="address"
          label="地址"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{ required: true, message: '请填写地址' }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-textarea
            v-model:value="formState.address"
            :rows="4"
            placeholder="请输入地址"
          />
        </a-form-item>

        <!-- 关注我们 -->
        <a-form-item
          name="follow_us"
          label="关注我们"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{ required: true, message: '请填写关注信息' }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            v-model:value="formState.follow_us"
            placeholder="请输入关注链接或信息"
          />
        </a-form-item>

        <!-- 图片上传 -->
        <a-form-item
          name="img"
          label="文件"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
         
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-upload
            :before-upload="handleBeforeUpload"
            :file-list="formState.img ? [{ uid: '-1', name: 'image.jpg', status: 'done', url: formState.img }] : []"
            :show-upload-list="{show: true, showRemoveIcon: true}"
              @remove="handleRemoveFile"
            :loading="uploading"
          >
            <a-button type="primary">
              <i class="fa fa-upload mr-1"></i> 选择文件
            </a-button>
          </a-upload>
          
          <!-- 图片预览 -->
          <div v-if="formState.img" class="mt-3">
            <!-- 根据文件后缀判断显示图片还是视频 -->
            <img
              v-if="formState.img.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/)"
              :src="formState.img"
              alt="预览图"
              class="w-40 h-40 object-cover rounded-md border border-gray-200 shadow-sm"
            />
            <video
              v-else-if="formState.img.toLowerCase().match(/\.(mp4|webm|ogg)$/)"
              :src="formState.img"
              controls
              class="w-40 h-40 object-cover rounded-md border border-gray-200 shadow-sm"
            />
          </div>
        </a-form-item>

        <!-- 提交按钮 -->
        <a-form-item :wrapper-col="{ span: 24 }" style="text-align: center">
          <a-button type="primary" @click="handleSubmit">
            提交表单
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </page-container>
</template>

<style scoped>
.ant-upload {
  width: 100%;
}

.ant-upload .ant-btn {
  width: 100%;
}

.ant-upload-list {
  margin-top: 16px;
}
</style>