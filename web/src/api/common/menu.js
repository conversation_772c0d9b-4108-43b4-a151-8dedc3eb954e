export function getMenusApi() {
  // return useGet('/v1/menus')
  return {
    code: 0,
    message: "ok",
    data: {
      list: [
        {
          id: 2,
          weight: 2,
          path: "/dashboard/messageindex", // 路由路径
          title: "消息索引页", // 菜单标题
          name: "DashboardMessageindex", // 路由名称（唯一）
          component: "/dashboard/messageindex/index", // 指向messageIndex目录下的index.vue
          locale: "作品管理",
          icon: "MessageOutlined", // 或其他合适的图标
          keepAlive: false,
          updatedAt: "2025-05-14 12:00:00",
        },
        //  {
        //   id: 3,
        //   weight: 4,
        //   path: "/dashboard/category", // 路由路径
        //   title: "消息索引页", // 菜单标题
        //   name: "DashboardCategory", // 路由名称（唯一）
        //   component: "/dashboard/category/index", // 指向messageIndex目录下的index.vue
        //   locale: "分类",
        //   icon: "MessageOutlined", // 或其他合适的图标
        //   keepAlive: false,
        //   updatedAt: "2025-05-14 12:00:00",
        // },
        
        {
          id: 4, // 假设新增数据的id为4，你可以根据实际情况修改
          weight: 4, // 权重，可按需修改
          path: "/dashboard/messagework", // 新增数据的路径
          title: "消息工作页", // 新增数据的菜单标题，你可以自定义
          name: "DashboardMessagework", // 新增数据的路由名称，需保持唯一
          component: "/dashboard/messagework/index", // 假设对应的组件路径，你可以根据实际情况修改
          locale: "轮播图", // 新增数据的国际化文案键名，可按需修改
          icon: "MessageOutlined", // 图标，可按需修改
          keepAlive: false, // 是否缓存，可按需修改
          updatedAt: "2025-05-14 12:00:00", // 更新时间，可按需修改
        },
         {
          id: 5, // 假设新增数据的id为4，你可以根据实际情况修改
          weight: 5, // 权重，可按需修改
          path: "/dashboard/member", // 新增数据的路径
          title: "成员", // 新增数据的菜单标题，你可以自定义
          name: "DashboardMember", // 新增数据的路由名称，需保持唯一
          component: "/dashboard/member/index", // 假设对应的组件路径，你可以根据实际情况修改
          locale: "成员", // 新增数据的国际化文案键名，可按需修改
          icon: "MessageOutlined", // 图标，可按需修改
          keepAlive: false, // 是否缓存，可按需修改
          updatedAt: "2025-05-14 12:00:00", // 更新时间，可按需修改
        },
        {
          id: 6, // 假设新增数据的id为4，你可以根据实际情况修改
          weight: 6, // 权重，可按需修改
          path: "/dashboard/about", // 新增数据的路径
          title: "关于我们", // 新增数据的菜单标题，你可以自定义
          name: "DashboardAbout", // 新增数据的路由名称，需保持唯一
          component: "/dashboard/about/index", // 假设对应的组件路径，你可以根据实际情况修改
          locale: "关于我们", // 新增数据的国际化文案键名，可按需修改
          icon: "MessageOutlined", // 图标，可按需修改
          keepAlive: false, // 是否缓存，可按需修改
          updatedAt: "2025-05-14 12:00:00", // 更新时间，可按需修改
        },
        
                 {
          id: 9, // 假设新增数据的id为4，你可以根据实际情况修改
          weight: 10, // 权重，可按需修改
          path: "/access/admin", // 新增数据的路径
          title: "账号管理", // 新增数据的菜单标题，你可以自定义
          name: "AccessAdmin", // 新增数据的路由名称，需保持唯一
          component: "/access/admin", // 假设对应的组件路径，你可以根据实际情况修改
          locale: "账号管理", // 新增数据的国际化文案键名，可按需修改
          icon: "MessageOutlined", // 图标，可按需修改
          keepAlive: false, // 是否缓存，可按需修改
          updatedAt: "2025-05-14 12:00:00", // 更新时间，可按需修改
        },

        // {
        //   id: 37,
        //   parentId: 36,
        //   weight: 0,
        //   path: "/account/center",
        //   title: "个人中心",
        //   name: "AccountCenter",
        //   component: "/account/center",
        //   locale: "menu.account.center",
        //   updatedAt: "2025-03-24 14:08:59",
        // },
        // {
        //   id: 38,
        //   parentId: 36,
        //   weight: 0,
        //   path: "/account/settings",
        //   title: "个人设置",
        //   name: "AccountSettings",
        //   component: "/account/settings",
        //   locale: "menu.account.settings",
        //   updatedAt: "2025-03-24 14:08:59",
        // },
      ],
    },
  };
}
export function getAdminMenusApi() {
  return useGet("/v1/admin/menus");
}
export function createMenuApi(params) {
  return usePost("/v1/admin/menu", params);
}
export function updateMenuApi(params) {
  return usePut("/v1/admin/menu", params);
}
export function deleteMenusApi(params) {
  return useDelete("/v1/admin/menu", params);
}
