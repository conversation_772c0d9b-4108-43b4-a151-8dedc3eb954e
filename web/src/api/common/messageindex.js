// api/messageIndex.js
export function createMessageIndexApi(params) {
  return usePost('/v1/messageIndex/create', params)
}

export function updateMessageIndexApi(params) {
  return usePut('/v1/messageIndex/update', params)
}

export function deleteMessageIndexApi(params) {
  return useDelete('/v1/messageIndex/delete', params)
}

export function getMessageIndexListApi(params) {
  return useGet('/v1/messageIndex/list', params)
}

export function getMessageIndexDetailApi(id) {
  // 注意：这里使用路径参数，而非查询参数
  return useGet(`/v1/messageIndex/detail/${id}`)
}
//新增编辑详情图
export function createOrUpdateMessageIndexDetailApi(params) {
  return usePost('/v1/messageIndexDetail/createOrUpdate', params);
}

//查看详情图列表
export function getMessageIndexDetail(id) {
  return useGet(`/v1/messageIndexDetail/GetMessageIndexDetail/${id}`);
}