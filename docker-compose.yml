version: '3'

services:
  aoneandatwo:
    image: g-pjih2197-docker.pkg.coding.net/guanwangxitong/aoneandatwo/aoneandatwo:latest
    container_name: aoneandatwo
    working_dir: /app
    command: 
      - ./aoneandatwo
      - -conf
      - config/prod.yml
    network_mode: host
    ports:
      - "8000:8000"
    volumes:
      - /website/aoneandatwo/config:/app/config:rw
      - /website/aoneandatwo/storage:/app/storage:rw
    restart: always
